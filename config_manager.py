#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 统一管理分布式文件处理系统的配置文件
支持所有四个核心模块的配置读取和验证

创建时间: 2025-01-25
"""

import os
import configparser
import logging
from typing import Dict, Any, List

class ConfigManager:
    """分布式文件处理系统配置管理器，负责读取和管理 config.ini 配置文件"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        初始化配置管理器
        
        :param config_path: 配置文件路径，默认为 config.ini
        """
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger('ConfigManager')
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        
        try:
            self.config.read(self.config_path, encoding='utf-8')
            self.logger.info(f"配置文件加载成功: {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"配置文件加载失败: {e}")
    
    def _get_boolean(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔值配置项"""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置项"""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """获取浮点数配置项"""
        try:
            return self.config.getfloat(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_string(self, section: str, key: str, default: str = "") -> str:
        """获取字符串配置项"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def _get_list(self, section: str, key: str, default: List[str] = None, separator: str = ';') -> List[str]:
        """获取列表配置项"""
        if default is None:
            default = []
        try:
            value = self.config.get(section, key)
            if not value:
                return default
            return [item.strip() for item in value.split(separator) if item.strip()]
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库模块配置"""
        return {
            'path': self._get_string('database', 'path', 'database.db'),
            'watch_directories': self._get_list('database', 'watch_directory', ['./data/watch'], ';'),
            'database_enable': self._get_boolean('database', 'database_enable', True),
            'max_connections': self._get_int('database', 'max_connections', 10),
            'connection_timeout': self._get_int('database', 'connection_timeout', 30),
            'retry_attempts': self._get_int('database', 'retry_attempts', 3),
            'retry_delay': self._get_float('database', 'retry_delay', 1.0),
            'wal_mode': self._get_boolean('database', 'wal_mode', True),
            'synchronous': self._get_string('database', 'synchronous', 'NORMAL'),
            'cache_size': self._get_int('database', 'cache_size', -64000),
            'temp_store': self._get_string('database', 'temp_store', 'MEMORY'),
            'mmap_size': self._get_int('database', 'mmap_size', 268435456),
            'journal_mode': self._get_string('database', 'journal_mode', 'WAL'),
            'foreign_keys': self._get_boolean('database', 'foreign_keys', True),
            'auto_vacuum': self._get_string('database', 'auto_vacuum', 'INCREMENTAL'),
            'busy_timeout': self._get_int('database', 'busy_timeout', 30000),
            'insert_sample_data': self._get_boolean('database', 'insert_sample_data', True),
        }
    
    def get_file_publish_config(self) -> Dict[str, Any]:
        """获取文件发布模块配置"""
        return {
            'watch_paths': self._get_list('file_publish', 'watch_paths', ['./data/watch'], ';'),
            'zmq_publish_addr': self._get_string('file_publish', 'zmq_publish_addr', 'tcp://*:5555'),
            'file_publish_enable': self._get_boolean('file_publish', 'file_publish_enable', True),
            'topics': self._get_list('file_publish', 'topics', ['leo_data'],';'),
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API服务模块配置"""
        return {
            'host': self._get_string('api', 'host', '0.0.0.0'),
            'port': self._get_int('api', 'port', 8000),
            'json_zmq_bind_addr': self._get_string('api', 'json_zmq_bind_addr', 'tcp://0.0.0.0:5556'),
            'topics': self._get_list('api', 'topics', ['api_responses'], ','),
            'api_enable': self._get_boolean('api', 'api_enable', True)
        }
    
    def get_subscribe_config(self) -> Dict[str, Any]:
        """获取订阅服务模块配置"""
        return {
            'save_directory': self._get_string('subscribe', 'save_directory', './data/save'),
            'zmq_connect_addrs': self._get_list('subscribe', 'zmq_connect_addrs', ['tcp://localhost:5555'], ','),
            'topics': self._get_list('subscribe', 'topics', ['file_updates', 'json_data'], ','),
            'subscribe_enable': self._get_boolean('subscribe', 'subscribe_enable', True)
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        level_str = self._get_string('logging', 'level', 'INFO').upper()
        
        # 将字符串转换为logging级别
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        level = level_map.get(level_str, logging.INFO)
        
        return {
            'level': level,
            'format': self._get_string('logging', 'format', '[%(asctime)s] [%(name)s] %(message)s'),
            'date_format': self._get_string('logging', 'date_format', '%Y-%m-%d %H:%M:%S'),
            'log_path': self._get_string('logging', 'log_path', './dataShare.log'),
        }

    def get_all_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return {
            'database': self.get_database_config(),
            'file_publish': self.get_file_publish_config(),
            'api': self.get_api_config(),
            'subscribe': self.get_subscribe_config(),
            'logging': self.get_logging_config()
        }
    
    def validate_config(self) -> bool:
        """验证配置文件的完整性和正确性"""
        try:
            # 检查必要的配置节是否存在
            required_sections = ['database', 'file_publish', 'api', 'subscribe', 'logging']
            for section in required_sections:
                if not self.config.has_section(section):
                    self.logger.error(f"缺少必要的配置节: [{section}]")
                    return False
            
            # 验证数据库配置
            db_config = self.get_database_config()
            if not db_config['path']:
                self.logger.error("数据库路径不能为空")
                return False
            
            # 验证API配置
            api_config = self.get_api_config()
            if not (1 <= api_config['port'] <= 65535):
                self.logger.error(f"API端口号无效: {api_config['port']}")
                return False
            
            # 验证文件发布配置
            fp_config = self.get_file_publish_config()
            if not fp_config['watch_paths']:
                self.logger.error("文件发布监控路径不能为空")
                return False
            
            # 验证订阅配置
            sub_config = self.get_subscribe_config()
            if not sub_config['zmq_connect_addrs']:
                self.logger.error("订阅连接地址不能为空")
                return False
            
            self.logger.info("配置文件验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def reload_config(self) -> bool:
        """重新加载配置文件"""
        try:
            self.load_config()
            return self.validate_config()
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
            return False
    
